const { FFCreator, FFScene, FFImage, FFText } = require('ffcreator');
const path = require('path');

// Simple test to verify image loading
async function testSimpleImage() {
    console.log('Testing simple image display...');
    
    const creator = new FFCreator({
        width: 854,
        height: 480,
        fps: 24,
        parallel: 2,
        debug: true
    });

    // Create a simple scene
    const scene = new FFScene();
    scene.setBgColor('#000000');
    scene.setDuration(5); // 5 seconds

    // Test image path
    const imagePath = path.join(__dirname, 'assets', '01_story_one', 'image_01_a.jpg');
    console.log('Testing image path:', imagePath);

    // Create image with simple configuration
    const image = new FFImage({
        path: imagePath,
        width: 854,
        height: 480,
        x: 0,
        y: 0
    });
    
    image.setDuration(5);
    image.setOpacity(1);
    
    console.log('Adding image to scene...');
    scene.addChild(image);

    // Create simple text
    const text = new FFText({
        text: 'TEST IMAGE DISPLAY',
        x: 50,
        y: 50,
        style: {
            fontSize: 48,
            fill: '#ffffff',
            fontWeight: 'bold'
        }
    });
    
    text.setDuration(5);
    text.setOpacity(1);
    text.setColor('#ffffff');
    
    console.log('Adding text to scene...');
    scene.addChild(text);

    creator.addChild(scene);

    creator.on('start', () => {
        console.log('FFCreator start');
    });

    creator.on('error', (e) => {
        console.error('FFCreator error:', e);
    });

    creator.on('progress', (e) => {
        console.log(`FFCreator progress: ${(e.percent * 100).toFixed(2)}%`);
    });

    creator.on('complete', (e) => {
        console.log('FFCreator completed successfully!');
        console.log('Output:', e.output);
    });

    const outputPath = 'test_simple_image.mp4';
    creator.output(outputPath);
    creator.start();
}

testSimpleImage().catch(console.error);

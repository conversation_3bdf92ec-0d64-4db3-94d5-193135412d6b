# FFCreatorLite DEMO

* #### Image animation

source: [https://github.com/drawcall/FFCreatorLite/blob/master/examples/image.js](https://github.com/drawcall/FFCreatorLite/blob/master/examples/image.js)

<video controls="controls" width="400" height="270" >
    <source type="video/mp4" src="./_media/video/lite/01.mp4"></source>
</video>

* #### Text animation

source: [https://github.com/drawcall/FFCreatorLite/blob/master/examples/text.js](https://github.com/drawcall/FFCreatorLite/blob/master/examples/text.js)

<video controls="controls" width="400" height="270" >
    <source type="video/mp4" src="./_media/video/lite/02.mp4"></source>
</video>

* #### Combined animation

source: [https://github.com/drawcall/FFCreatorLite/blob/master/examples/animate.js](https://github.com/drawcall/FFCreatorLite/blob/master/examples/animate.js)

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/lite/03.mp4"></source>
</video>

* #### Video animation

source: [https://github.com/drawcall/FFCreatorLite/blob/master/examples/video.js](https://github.com/drawcall/FFCreatorLite/blob/master/examples/video.js)

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/lite/04.mp4"></source>
</video>

// Type definitions for FFCreator v6.2.1
// Project: https://github.com/tnfe/FFCreator
// Definitions by: javaswing <https://github.com/javaswing>
// TypeScript Version: 4.2


/// <reference path="./lib/FFCreator.d.ts" />
/// <reference path="./lib/FFNode.d.ts" />
/// <reference path="./lib/FFText.d.ts" />
/// <reference path="./lib/FFImage.d.ts" />
/// <reference path="./lib/FFAudio.d.ts" />
/// <reference path="./lib/FFGifImage.d.ts" />
/// <reference path="./lib/FFAnimation.d.ts" />
/// <reference path="./lib/FFAudio.d.ts" />
/// <reference path="./lib/FFVideo.d.ts" />
/// <reference path="./lib/FFVideoAlbum.d.ts" />
/// <reference path="./lib/FFAlbum.d.ts" />
/// <reference path="./lib/FFSubtitle.d.ts" />
/// <reference path="./lib/FFScene.d.ts" />
/// <reference path="./lib/FFVtuber.d.ts" />
/// <reference path="./lib/FFChart.d.ts" />
/// <reference path="./lib/FFRect.d.ts" />
/// <reference path="./lib/FFExtras.d.ts" />
/// <reference path="./lib/FFmpegUtil.d.ts" />
/// <reference path="./lib/FFLogger.d.ts" />
/// <reference path="./lib/FFTween.d.ts" />
/// <reference path="./lib/FFCreatorCenter.d.ts" />

export = FFCreatorSpace;

export as namespace ffcreator;

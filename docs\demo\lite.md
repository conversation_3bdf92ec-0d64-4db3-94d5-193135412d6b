# FFCreatorLite DEMO

* #### 图片动画

source: [https://github.com/drawcall/FFCreatorLite/blob/master/examples/image.js](https://github.com/drawcall/FFCreatorLite/blob/master/examples/image.js)

<video controls="controls" width="400" height="270" >
    <source type="video/mp4" src="./_media/video/lite/01.mp4"></source>
</video>

* #### 文字动画

source: [https://github.com/drawcall/FFCreatorLite/blob/master/examples/text.js](https://github.com/drawcall/FFCreatorLite/blob/master/examples/text.js)

<video controls="controls" width="400" height="270" >
    <source type="video/mp4" src="./_media/video/lite/02.mp4"></source>
</video>

* #### 各种组合动画

source: [https://github.com/drawcall/FFCreatorLite/blob/master/examples/animate.js](https://github.com/drawcall/FFCreatorLite/blob/master/examples/animate.js)

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/lite/03.mp4"></source>
</video>

* #### 视频动画

source: [https://github.com/drawcall/FFCreatorLite/blob/master/examples/video.js](https://github.com/drawcall/FFCreatorLite/blob/master/examples/video.js)

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/lite/04.mp4"></source>
</video>


* #### 牛掰小视频

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/lite/05.mov"></source>
</video>

const path = require('path');
const fs = require('fs');
const { FFCreator, FFScene, FFImage, FFText, FFVideo, FFAudio } = require('ffcreator');
const musicMetadata = require('music-metadata');

// Set FFmpeg and FFprobe paths
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const ffprobePath = require('ffprobe-installer').path;
FFCreator.setFFmpegPath(ffmpegPath);
FFCreator.setFFprobePath(ffprobePath);

const projectRoot = __dirname;
const configPath = path.join(projectRoot, 'config.json');
const assetsRoot = path.join(projectRoot, 'assets');
const commonAssetsPath = path.join(assetsRoot, 'common');

const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));

function createTitle(scene, textContent, startTime, duration, sceneDuration, totalTexts) {
    const title = new FFText({
        text: textContent,
        x: 50,
        y: config.height - 120,
        style: {
            fontSize: 32,
            fill: '#ffffff',
            fontWeight: 'bold',
            wordWrap: true,
            wordWrapWidth: config.width - 100,
            padding: 20,
            backgroundColor: '#000000CC',
            borderRadius: 8
        }
    });

    // Set colors explicitly
    title.setColor('#ffffff');
    title.setBackgroundColor('#000000CC');

    console.log(`Text created: "${textContent}" at (50, ${config.height - 120}) size ${32}px`);

    // Set full scene duration
    title.setDuration(sceneDuration);

    if (totalTexts === 1) {
        // Single text - show for full duration
        title.setOpacity(1);
        console.log(`Single text title visible for full duration: "${textContent}"`);
    } else {
        // Multiple texts - sequential display
        title.setOpacity(0);

        // Fade in at designated time
        title.addAnimate({
            from: { opacity: 0 },
            to: { opacity: 1 },
            time: 0.5,
            delay: startTime
        });

        // Fade out before next title (except for last title)
        const endTime = startTime + duration;
        if (endTime < sceneDuration - 0.5) {
            title.addAnimate({
                from: { opacity: 1 },
                to: { opacity: 0 },
                time: 0.5,
                delay: endTime - 0.5
            });
        }

        console.log(`Text title scheduled: "${textContent}" (${startTime}s - ${endTime}s)`);
    }

    scene.addChild(title);
    console.log(`✅ Added text to scene: "${textContent}"`);
}

function addLogoToScene(scene) {
    const logoPath = path.join(commonAssetsPath, 'channel_logo.png');
    if (fs.existsSync(logoPath)) {
        const logo = new FFImage({
            path: logoPath,
            x: config.width - 80,  // Top right corner
            y: 60,
            width: 120,
            height: 60
        });
        logo.setScale(0.8);
        scene.addChild(logo);
    }
}

function findBackgroundMusic(clipFolderName, clipAssetPath) {
    // Priority 1: Look for background_music.mp3 inside the current story folder
    const localMusicPath = path.join(clipAssetPath, 'background_music.mp3');
    if (fs.existsSync(localMusicPath)) {
        console.log(`Using local background music: ${localMusicPath}`);
        return localMusicPath;
    }

    // Priority 2: Look for named file in common (e.g., 01_background_music.mp3)
    const storyNumber = clipFolderName.split('_')[0];
    const namedCommonMusicPath = path.join(commonAssetsPath, `${storyNumber}_background_music.mp3`);
    if (fs.existsSync(namedCommonMusicPath)) {
        console.log(`Using named background music: ${namedCommonMusicPath}`);
        return namedCommonMusicPath;
    }

    // Priority 3: Use default background_music.mp3 from common folder
    const defaultCommonMusicPath = path.join(commonAssetsPath, 'background_music.mp3');
    if (fs.existsSync(defaultCommonMusicPath)) {
        console.log(`Using default background music: ${defaultCommonMusicPath}`);
        return defaultCommonMusicPath;
    }

    console.log(`No background music found for ${clipFolderName}`);
    return null;
}

(async () => {
    console.log('Starting video creation script...');

    const outputDir = path.join(projectRoot, 'output/');
    const cacheDir = path.join(projectRoot, 'cache/');

    const creator = new FFCreator({
        cacheDir,
        outputDir,
        width: config.width,
        height: config.height,
        fps: 24, // Reduced FPS for faster rendering
        log: true,
        highWaterMark: '3mb'
    });

    // Note: Logo parameter is not supported in constructor.
    // If you need a logo overlay, add it as an FFImage to each scene instead.

    const sortedClipFolders = fs.readdirSync(assetsRoot, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory() && dirent.name !== 'common')
        .map(dirent => dirent.name)
        .sort();

    // Add intro video in a scene
    const introPath = path.join(commonAssetsPath, 'intro.mp4');
    if (fs.existsSync(introPath)) {
        const introScene = new FFScene();
        introScene.setDuration(3); // Set a default duration, adjust as needed
        introScene.setBgColor('#000000');

        const introVideo = new FFVideo({
            path: introPath,
            width: config.width,
            height: config.height,
            x: config.width / 2,
            y: config.height / 2
        });
        introScene.addChild(introVideo);
        // No logo on intro scene as per specification
        creator.addChild(introScene);
    }
    
    for (let i = 0; i < sortedClipFolders.length; i++) {
        const clipFolderName = sortedClipFolders[i];
        const clipAssetPath = path.join(assetsRoot, clipFolderName);
        const allFiles = fs.readdirSync(clipAssetPath);
        const audioFiles = allFiles
            .filter(f => f.match(/^audio_(\d+).*\.(mp3|wav)$/))
            .sort((a, b) => parseInt(a.match(/(\d+)/)[0]) - parseInt(b.match(/(\d+)/)[0]));
        
        for (const audioFile of audioFiles) {
            try {
                const subClipNumber = audioFile.match(/(\d+)/)[0];
                const audioPath = path.join(clipAssetPath, audioFile);
                console.log(`Processing audio file: ${audioFile}`);

                const metadata = await musicMetadata.parseFile(audioPath);
                const sceneDuration = metadata.format.duration;

                if (!sceneDuration || sceneDuration <= 0) {
                    console.warn(`Invalid duration for ${audioFile}, skipping...`);
                    continue;
                }

                const scene = new FFScene();
                scene.setDuration(sceneDuration);
                scene.setBgColor('#000000'); // Set black background

                // Add logo to each scene
                addLogoToScene(scene);

            const brollFiles = allFiles.filter(f => f.match(new RegExp(`^broll_${subClipNumber}.*\\.mp4$`)));
            let visuals;
            if (brollFiles.length > 0) {
                visuals = brollFiles.map(file => ({ path: path.join(clipAssetPath, file), type: 'video' }));
            } else {
                const imageFiles = allFiles.filter(f => f.match(new RegExp(`^image_${subClipNumber}.*\\.(jpg|jpeg|png)$`)));
                if (imageFiles.length > 0) {
                    visuals = imageFiles.map(file => ({ path: path.join(clipAssetPath, file), type: 'image' }));
                }
            }

            let hasVisual = false;
            console.log(`DEBUG: visuals array:`, visuals);
            if (visuals && visuals.length > 0) {
                console.log(`Adding ${visuals.length} visuals to scene`);
                const durationPerVisual = sceneDuration / visuals.length;

                // For multiple visuals, we need to handle them sequentially
                // Since FFCreator plays all objects in a scene simultaneously,
                // we'll use opacity animations to show/hide them in sequence
                visuals.forEach((visual, index) => {
                    let mediaObject;
                    if (visual.type === 'video') {
                        mediaObject = new FFVideo({
                            path: visual.path,
                            width: config.width,
                            height: config.height,
                            x: 0,
                            y: 0
                        });
                        mediaObject.setLoop(true);
                        console.log(`Video configured: ${config.width}x${config.height} at (0,0)`);
                    } else {
                        mediaObject = new FFImage({
                            path: visual.path,
                            x: config.width / 2,
                            y: config.height / 2
                        });
                        mediaObject.setScale(1);
                        console.log(`Image configured: centered at (${config.width / 2}, ${config.height / 2})`);
                    }

                    mediaObject.setDuration(sceneDuration); // Full scene duration

                    const startTime = index * durationPerVisual;
                    const endTime = (index + 1) * durationPerVisual;

                    console.log(`Adding ${visual.type}: ${visual.path} (${startTime}s - ${endTime}s)`);

                    if (visuals.length === 1) {
                        // Single visual - show for full duration
                        mediaObject.setOpacity(1);
                        console.log(`Single ${visual.type} visible for full duration`);
                    } else {
                        // Multiple visuals - sequential display
                        mediaObject.setOpacity(0);

                        // Fade in at designated time
                        mediaObject.addAnimate({
                            from: { opacity: 0 },
                            to: { opacity: 1 },
                            time: 0.5,
                            delay: startTime
                        });

                        // Fade out before next visual (except for last visual)
                        if (index < visuals.length - 1) {
                            mediaObject.addAnimate({
                                from: { opacity: 1 },
                                to: { opacity: 0 },
                                time: 0.5,
                                delay: endTime - 0.5
                            });
                        }

                        console.log(`${visual.type} ${index + 1}/${visuals.length} scheduled: ${startTime}s - ${endTime}s`);
                    }

                    scene.addChild(mediaObject);
                    hasVisual = true;
                    console.log(`✅ Added ${visual.type} to scene: ${visual.path}`);
                });
            }
            if (!hasVisual) {
                const placeholderVideo = new FFVideo({ path: path.join(commonAssetsPath, 'default_placeholder.mp4') });
                placeholderVideo.setLoop(true);
                scene.addChild(placeholderVideo);
                hasVisual = true;
            }
            
            const textFiles = allFiles.filter(f => f.match(new RegExp(`^text_${subClipNumber}.*\\.txt$`)));
            console.log(`DEBUG: textFiles array:`, textFiles);
            let hasText = false;
            if(textFiles.length > 0) {
                console.log(`Adding ${textFiles.length} text titles to scene`);
                const durationPerText = sceneDuration / textFiles.length;
                textFiles.forEach((file, index) => {
                    const textContent = fs.readFileSync(path.join(clipAssetPath, file), 'utf-8');
                    const startTime = index * durationPerText;
                    createTitle(scene, textContent.trim(), startTime, durationPerText, sceneDuration, textFiles.length);
                    hasText = true;
                });
            }
            // Only add scene if it has at least one child (visual or text)
            console.log(`DEBUG: hasVisual=${hasVisual}, hasText=${hasText}`);
            if (hasVisual || hasText) {
                console.log(`Adding scene to creator with ${hasVisual ? 'visuals' : ''} ${hasText ? 'text' : ''}`);
                // Add transition effect to scene (only if it will be added)
                scene.setTransition('fadeIn', 1.0);

                // Add narration audio to the scene (increased volume for narrator)
                const narrationAudio = new FFAudio({
                    path: audioPath,
                    volume: 2.0, // Increased volume for narrator
                    fadeIn: 0.5,
                    fadeOut: 0.5
                });
                scene.addAudio(narrationAudio);

                // Add background music for this scene using 3-level priority
                const backgroundMusicPath = findBackgroundMusic(clipFolderName, clipAssetPath);
                if (backgroundMusicPath) {
                    const backgroundMusic = new FFAudio({
                        path: backgroundMusicPath,
                        volume: 1.0, // Full volume as specified - no automatic reduction
                        loop: true,
                        fadeIn: 1.0,
                        fadeOut: 1.0
                    });
                    scene.addAudio(backgroundMusic);
                }

                creator.addChild(scene);
            }
            } catch (error) {
                console.error(`Error processing audio file ${audioFile}:`, error);
                continue;
            }
        }

        // Add transition between stories (not after the last story)
        if (i < sortedClipFolders.length - 1) {
            // First check for transition.mp4 in the story folder that just completed
            let transitionPath = path.join(clipAssetPath, 'transition.mp4');
            if (!fs.existsSync(transitionPath)) {
                // If not found, use default transition.mp4 from common folder
                transitionPath = path.join(commonAssetsPath, 'transition.mp4');
            }

            if (fs.existsSync(transitionPath)) {
                console.log(`Using transition: ${transitionPath}`);
                const transitionScene = new FFScene();
                transitionScene.setDuration(2); // Set a default duration
                transitionScene.setBgColor('#000000');

                const transitionVideo = new FFVideo({
                    path: transitionPath,
                    width: config.width,
                    height: config.height,
                    x: config.width / 2,
                    y: config.height / 2
                });
                transitionScene.addChild(transitionVideo);
                addLogoToScene(transitionScene); // Add logo to transition too
                creator.addChild(transitionScene);
            }
        }
    }

    // Background music is now handled per scene according to 3-level priority system

    // Add outro video in a scene
    const outroPath = path.join(commonAssetsPath, 'outro.mp4');
    if (fs.existsSync(outroPath)) {
        const outroScene = new FFScene();
        outroScene.setDuration(3); // Set a default duration, adjust as needed
        outroScene.setBgColor('#000000');

        const outroVideo = new FFVideo({
            path: outroPath,
            width: config.width,
            height: config.height,
            x: config.width / 2,
            y: config.height / 2
        });
        outroScene.addChild(outroVideo);
        // No logo on outro scene as per specification
        creator.addChild(outroScene);
    }
    
    creator.on('start', () => {
        console.log('FFCreator video creation started...');
    });

    creator.on('progress', e => {
        console.log(`FFCreator progress: ${(e.percent * 100).toFixed(2)}%`);
    });

    creator.on('complete', e => {
        console.log(`\nFFCreator completed successfully!`);
        console.log(`Usage: ${e.useage}`);
        console.log(`Output: ${e.output}`);
    });

    creator.on('error', e => {
        console.error(`FFCreator error: ${JSON.stringify(e)}`);
    });

    creator.start();
})();
const path = require('path');
const fs = require('fs');
const { FFCreator, FFScene, FFImage, FFText, FFVideo, FFAudio } = require('ffcreator');
const musicMetadata = require('music-metadata');

const projectRoot = __dirname;
const configPath = path.join(projectRoot, 'config.json');
const assetsRoot = path.join(projectRoot, 'assets');
const commonAssetsPath = path.join(assetsRoot, 'common');

const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));

function createTitle(scene, textContent, startTime) {
    const title = new FFText({
        text: textContent,
        x: config.width / 2,
        y: config.height - 100,
        fontSize: 32
    });
    title.setColor('#ffffff');
    title.setBackgroundColor('#003366CC');
    title.setStyle({
        fontSize: 32,
        padding: 15,
        fontWeight: 'bold',
        wordWrap: true,
        wordWrapWidth: config.width - 40
    });
    title.alignCenter();
    title.addEffect('slideInLeft', 1, startTime);
    scene.addChild(title);
}

function addLogoToScene(scene) {
    const logoPath = path.join(commonAssetsPath, 'channel_logo.png');
    if (fs.existsSync(logoPath)) {
        const logo = new FFImage({
            path: logoPath,
            x: config.width - 80,  // Top right corner
            y: 60,
            width: 120,
            height: 60
        });
        logo.setScale(0.8);
        scene.addChild(logo);
    }
}

function findBackgroundMusic(clipFolderName) {
    const localMusicPath = path.join(assetsRoot, clipFolderName, 'background_music.mp3');
    if (fs.existsSync(localMusicPath)) return localMusicPath;
    const namedCommonMusicPath = path.join(commonAssetsPath, `${clipFolderName.split('_')[0]}_background_music.mp3`);
    if (fs.existsSync(namedCommonMusicPath)) return namedCommonMusicPath;
    const defaultCommonMusicPath = path.join(commonAssetsPath, 'background_music.mp3');
    if (fs.existsSync(defaultCommonMusicPath)) return defaultCommonMusicPath;
    return null;
}

(async () => {
    console.log('Starting video creation script...');
    console.log('Project root:', projectRoot);
    console.log('Config:', config);

    const outputDir = path.join(projectRoot, 'output/');
    const cacheDir = path.join(projectRoot, 'cache/');

    const creator = new FFCreator({
        cacheDir,
        outputDir,
        width: config.width,
        height: config.height,
        fps: 30,
        log: true,
        highWaterMark: '3mb'
    });

    // Note: Logo parameter is not supported in constructor.
    // If you need a logo overlay, add it as an FFImage to each scene instead.

    const sortedClipFolders = fs.readdirSync(assetsRoot, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory() && dirent.name !== 'common')
        .map(dirent => dirent.name)
        .sort();

    // Add intro video in a scene
    const introPath = path.join(commonAssetsPath, 'intro.mp4');
    if (fs.existsSync(introPath)) {
        const introScene = new FFScene();
        introScene.setDuration(3); // Set a default duration, adjust as needed
        introScene.setBgColor('#000000');

        const introVideo = new FFVideo({
            path: introPath,
            width: config.width,
            height: config.height,
            x: config.width / 2,
            y: config.height / 2
        });
        introScene.addChild(introVideo);
        addLogoToScene(introScene);
        creator.addChild(introScene);
    }
    
    for (let i = 0; i < sortedClipFolders.length; i++) {
        const clipFolderName = sortedClipFolders[i];
        const clipAssetPath = path.join(assetsRoot, clipFolderName);
        const allFiles = fs.readdirSync(clipAssetPath);
        const audioFiles = allFiles
            .filter(f => f.match(/^audio_(\d+).*\.(mp3|wav)$/))
            .sort((a, b) => parseInt(a.match(/(\d+)/)[0]) - parseInt(b.match(/(\d+)/)[0]));
        
        for (const audioFile of audioFiles) {
            try {
                const subClipNumber = audioFile.match(/(\d+)/)[0];
                const audioPath = path.join(clipAssetPath, audioFile);
                console.log(`Processing audio file: ${audioFile}`);

                const metadata = await musicMetadata.parseFile(audioPath);
                const sceneDuration = metadata.format.duration;

                if (!sceneDuration || sceneDuration <= 0) {
                    console.warn(`Invalid duration for ${audioFile}, skipping...`);
                    continue;
                }

                const scene = new FFScene();
                scene.setDuration(sceneDuration);
                scene.setBgColor('#000000'); // Set black background

                // Add logo to each scene
                addLogoToScene(scene);

            const brollFiles = allFiles.filter(f => f.match(new RegExp(`^broll_${subClipNumber}.*\\.mp4$`)));
            let visuals;
            if (brollFiles.length > 0) {
                visuals = brollFiles.map(file => ({ path: path.join(clipAssetPath, file), type: 'video' }));
            } else {
                const imageFiles = allFiles.filter(f => f.match(new RegExp(`^image_${subClipNumber}.*\\.(jpg|jpeg|png)$`)));
                if (imageFiles.length > 0) {
                    visuals = imageFiles.map(file => ({ path: path.join(clipAssetPath, file), type: 'image' }));
                }
            }

            let hasVisual = false;
            if (visuals && visuals.length > 0) {
                const durationPerVisual = sceneDuration / visuals.length;
                visuals.forEach((visual, index) => {
                    let mediaObject;
                    if (visual.type === 'video') {
                        mediaObject = new FFVideo({ path: visual.path });
                        mediaObject.setLoop(true);
                    } else {
                        mediaObject = new FFImage({ path: visual.path });
                    }
                    mediaObject.setXY(config.width / 2, config.height / 2);
                    mediaObject.setDuration(durationPerVisual);
                    if (visual.type === 'image') {
                        mediaObject.addEffect('zoomIn', durationPerVisual, index * durationPerVisual);
                    } else {
                        mediaObject.setStartTime(index * durationPerVisual);
                    }
                    scene.addChild(mediaObject);
                    hasVisual = true;
                });
            }
            if (!hasVisual) {
                const placeholderVideo = new FFVideo({ path: path.join(commonAssetsPath, 'default_placeholder.mp4') });
                placeholderVideo.setLoop(true);
                scene.addChild(placeholderVideo);
                hasVisual = true;
            }
            
            const textFiles = allFiles.filter(f => f.match(new RegExp(`^text_${subClipNumber}.*\\.txt$`)));
            let hasText = false;
            if(textFiles.length > 0) {
                const durationPerText = sceneDuration / textFiles.length;
                textFiles.forEach((file, index) => {
                    const textContent = fs.readFileSync(path.join(clipAssetPath, file), 'utf-8');
                    createTitle(scene, textContent.trim(), index * durationPerText);
                    hasText = true;
                });
            }
            // Only add scene if it has at least one child (visual or text)
            if (hasVisual || hasText) {
                // Add transition effect to scene (only if it will be added)
                scene.setTransition('fadeIn', 1.0);

                // Add narration audio to the scene instead of creator for better sync
                const narrationAudio = new FFAudio({
                    path: audioPath,
                    volume: 1.0,
                    fadeIn: 0.5,
                    fadeOut: 0.5
                });
                scene.addAudio(narrationAudio);
                creator.addChild(scene);
            }
            } catch (error) {
                console.error(`Error processing audio file ${audioFile}:`, error);
                continue;
            }
        }

        if (i < sortedClipFolders.length - 1) {
            const transitionPath = path.join(commonAssetsPath, 'transition.mp4');
            if (fs.existsSync(transitionPath)) {
                const transitionScene = new FFScene();
                transitionScene.setDuration(2); // Set a default duration
                transitionScene.setBgColor('#000000');

                const transitionVideo = new FFVideo({
                    path: transitionPath,
                    width: config.width,
                    height: config.height,
                    x: config.width / 2,
                    y: config.height / 2
                });
                transitionScene.addChild(transitionVideo);
                creator.addChild(transitionScene);
            }
        }
    }

    // Add global background music if available
    const globalBackgroundMusic = path.join(commonAssetsPath, 'background_music.mp3');
    if (fs.existsSync(globalBackgroundMusic)) {
        creator.addAudio(new FFAudio({
            path: globalBackgroundMusic,
            loop: true,
            volume: 0.1,
            fadeIn: 2.0,
            fadeOut: 2.0
        }));
    }

    // Add outro video in a scene
    const outroPath = path.join(commonAssetsPath, 'outro.mp4');
    if (fs.existsSync(outroPath)) {
        const outroScene = new FFScene();
        outroScene.setDuration(3); // Set a default duration, adjust as needed
        outroScene.setBgColor('#000000');

        const outroVideo = new FFVideo({
            path: outroPath,
            width: config.width,
            height: config.height,
            x: config.width / 2,
            y: config.height / 2
        });
        outroScene.addChild(outroVideo);
        addLogoToScene(outroScene);
        creator.addChild(outroScene);
    }
    
    creator.on('start', () => {
        console.log('FFCreator video creation started...');
    });

    creator.on('progress', e => {
        console.log(`FFCreator progress: ${(e.percent * 100).toFixed(2)}%`);
    });

    creator.on('complete', e => {
        console.log(`\nFFCreator completed successfully!`);
        console.log(`Usage: ${e.useage}`);
        console.log(`Output: ${e.output}`);
    });

    creator.on('error', e => {
        console.error(`FFCreator error: ${JSON.stringify(e)}`);
    });

    creator.start();
})();
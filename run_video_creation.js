const path = require('path');
const fs = require('fs');
const { FFCreator, FFScene, FFImage, FFText, FFVideo, FFAudio } = require('ffcreator');
const musicMetadata = require('music-metadata');

const projectRoot = __dirname;
const configPath = path.join(projectRoot, 'config.json');
const assetsRoot = path.join(projectRoot, 'assets');
const commonAssetsPath = path.join(assetsRoot, 'common');

const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));

function createTitle(scene, textContent, startTime, duration) {
    const title = new FFText({
        text: textContent,
        x: 20,
        y: 900
    });
    title.setStyle({ fontSize: 70, padding: 25 });
    title.setBackgroundColor('#003366CC');
    // No direct support for start/duration, so use addEffect for timing
    title.addEffect('slideInLeft', 1, startTime);
    scene.addChild(title);
}

function findBackgroundMusic(clipFolderName) {
    const localMusicPath = path.join(assetsRoot, clipFolderName, 'background_music.mp3');
    if (fs.existsSync(localMusicPath)) return localMusicPath;
    const namedCommonMusicPath = path.join(commonAssetsPath, `${clipFolderName.split('_')[0]}_background_music.mp3`);
    if (fs.existsSync(namedCommonMusicPath)) return namedCommonMusicPath;
    const defaultCommonMusicPath = path.join(commonAssetsPath, 'background_music.mp3');
    if (fs.existsSync(defaultCommonMusicPath)) return defaultCommonMusicPath;
    return null;
}

(async () => {
    const creator = new FFCreator({
        width: config.width,
        height: config.height,
        fps: 30,
        cacheDir: path.join(projectRoot, 'cache/')
    });
    creator.setOutput(path.join(projectRoot, config.output_file));

    // Note: Logo parameter is not supported in constructor.
    // If you need a logo overlay, add it as an FFImage to each scene instead.

    const sortedClipFolders = fs.readdirSync(assetsRoot, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory() && dirent.name !== 'common')
        .map(dirent => dirent.name)
        .sort();

    creator.addChild(new FFVideo({
        path: path.join(commonAssetsPath, 'intro.mp4'),
        width: config.width,
        height: config.height,
    }));
    
    for (let i = 0; i < sortedClipFolders.length; i++) {
        const clipFolderName = sortedClipFolders[i];
        const clipAssetPath = path.join(assetsRoot, clipFolderName);
        const backgroundMusicPath = findBackgroundMusic(clipFolderName);
        const allFiles = fs.readdirSync(clipAssetPath);
        const audioFiles = allFiles
            .filter(f => f.match(/^audio_(\d+).*\.(mp3|wav)$/))
            .sort((a, b) => parseInt(a.match(/(\d+)/)[0]) - parseInt(b.match(/(\d+)/)[0]));
        
        for (const audioFile of audioFiles) {
            const subClipNumber = audioFile.match(/(\d+)/)[0];
            const audioPath = path.join(clipAssetPath, audioFile);
            const metadata = await musicMetadata.parseFile(audioPath);
            const sceneDuration = metadata.format.duration;
            const scene = new FFScene();
            scene.setDuration(sceneDuration);
            scene.setTransition('fade', 1); // Add a default transition to avoid undefined

            const brollFiles = allFiles.filter(f => f.match(new RegExp(`^broll_${subClipNumber}.*\\.mp4$`)));
            let visuals;
            if (brollFiles.length > 0) {
                visuals = brollFiles.map(file => ({ path: path.join(clipAssetPath, file), type: 'video' }));
            } else {
                const imageFiles = allFiles.filter(f => f.match(new RegExp(`^image_${subClipNumber}.*\\.(jpg|jpeg|png)$`)));
                if (imageFiles.length > 0) {
                    visuals = imageFiles.map(file => ({ path: path.join(clipAssetPath, file), type: 'image' }));
                }
            }

            let hasVisual = false;
            if (visuals && visuals.length > 0) {
                const durationPerVisual = sceneDuration / visuals.length;
                visuals.forEach((visual, index) => {
                    let mediaObject;
                    if (visual.type === 'video') {
                        mediaObject = new FFVideo({ path: visual.path });
                        mediaObject.setLoop(true);
                    } else {
                        mediaObject = new FFImage({ path: visual.path });
                    }
                    mediaObject.setXY(config.width / 2, config.height / 2);
                    mediaObject.setDuration(durationPerVisual);
                    if (visual.type === 'image') {
                        mediaObject.addEffect('zoomIn', durationPerVisual, index * durationPerVisual);
                    } else {
                        mediaObject.setStartTime(index * durationPerVisual);
                    }
                    scene.addChild(mediaObject);
                    hasVisual = true;
                });
            }
            if (!hasVisual) {
                const placeholderVideo = new FFVideo({ path: path.join(commonAssetsPath, 'default_placeholder.mp4') });
                placeholderVideo.setLoop(true);
                scene.addChild(placeholderVideo);
                hasVisual = true;
            }
            
            const textFiles = allFiles.filter(f => f.match(new RegExp(`^text_${subClipNumber}.*\\.txt$`)));
            let hasText = false;
            if(textFiles.length > 0) {
                const durationPerText = sceneDuration / textFiles.length;
                textFiles.forEach((file, index) => {
                    const textContent = fs.readFileSync(path.join(clipAssetPath, file), 'utf-8');
                    createTitle(scene, textContent.trim(), index * durationPerText, durationPerText);
                    hasText = true;
                });
            }
            // Only add scene if it has at least one child (visual or text)
            if (hasVisual || hasText) {
                creator.addChild(scene);
                const narrationAudio = new FFAudio({ path: audioPath, volume: 1.0, fadeIn: 0.5, fadeOut: 0.5 });
                creator.addAudio(narrationAudio);
            }
        }
        
        if (backgroundMusicPath) {
            creator.addAudio(new FFAudio({ path: backgroundMusicPath, loop: true, volume: 0.1, fadeIn: 2.0 }));
        }

        if (i < sortedClipFolders.length - 1) {
             creator.addChild(new FFVideo({
                path: path.join(commonAssetsPath, 'transition.mp4'),
                width: config.width,
                height: config.height,
             }));
        }
    }

    creator.addChild(new FFVideo({
        path: path.join(commonAssetsPath, 'outro.mp4'),
        width: config.width,
        height: config.height,
    }));
    
    creator.on('start', () => console.log('Video creation started.'));
    creator.on('progress', e => console.log(`Progress: ${(e.percent * 100).toFixed(2)}%`));
    creator.on('complete', e => console.log(`\nVideo creation complete! Output: ${e.output}`));
    creator.on('error', e => console.error(`Error during video creation:`, e));
    creator.start();
})();
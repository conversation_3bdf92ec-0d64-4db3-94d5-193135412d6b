# FFCreator DEMO

* #### Image animation

source: [https://github.com/tnfe/FFCreator/blob/master/examples/image.js](https://github.com/tnfe/FFCreator/blob/master/examples/image.js)

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/normal/01.mp4"></source>
</video>

* #### Multi-photo album

source: [https://github.com/tnfe/FFCreator/blob/master/examples/album.js](https://github.com/tnfe/FFCreator/blob/master/examples/album.js)

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/normal/02.mp4"></source>
</video>

* #### Scene transition

source: [https://github.com/tnfe/FFCreator/blob/master/examples/transition.js](https://github.com/tnfe/FFCreator/blob/master/examples/transition.js)

<video controls="controls" width="400" height="270" >
    <source type="video/mp4" src="./_media/video/normal/03.mp4"></source>
</video>

* #### Dubbing

source: [https://github.com/tnfe/FFCreator/blob/master/examples/subtitle.js](https://github.com/tnfe/FFCreator/blob/master/examples/subtitle.js)

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/normal/04.mp4"></source>
</video>

* #### Video animation

source: [https://github.com/tnfe/FFCreator/blob/master/examples/video.js](https://github.com/tnfe/FFCreator/blob/master/examples/video.js)

Note: If you have stronger video production requirements, please use [`FFCreatorLite`](guide/lite.md), `FFCreatorLite` video processing efficiency is far better than `FFCreator`.

<video controls="controls" width="400" height="300" >
    <source type="video/mp4" src="./_media/video/normal/05.mp4"></source>
</video>

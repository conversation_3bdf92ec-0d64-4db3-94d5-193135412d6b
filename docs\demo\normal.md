# FFCreator DEMO

* #### 图片动画

source: [https://github.com/tnfe/FFCreator/blob/master/examples/image.js](https://github.com/tnfe/FFCreator/blob/master/examples/image.js)

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/normal/01.mp4"></source>
</video>

* #### 多图相册

source: [https://github.com/tnfe/FFCreator/blob/master/examples/album.js](https://github.com/tnfe/FFCreator/blob/master/examples/album.js)

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/normal/02.mp4"></source>
</video>

* #### 场景过渡

source: [https://github.com/tnfe/FFCreator/blob/master/examples/transition.js](https://github.com/tnfe/FFCreator/blob/master/examples/transition.js)

<video controls="controls" width="400" height="270" >
    <source type="video/mp4" src="./_media/video/normal/03.mp4"></source>
</video>

* #### 配音字幕

source: [https://github.com/tnfe/FFCreator/blob/master/examples/subtitle.js](https://github.com/tnfe/FFCreator/blob/master/examples/subtitle.js)

<video controls="controls" width="350" height="622" >
    <source type="video/mp4" src="./_media/video/normal/04.mp4"></source>
</video>

* #### 视频动画

source: [https://github.com/tnfe/FFCreator/blob/master/examples/video.js](https://github.com/tnfe/FFCreator/blob/master/examples/video.js)

注: 如果您有更强的视频制作需求请使用[`FFCreatorLite`](guide/lite.md), `FFCreatorLite`视频处理效率远胜于`FFCreator`。

<video controls="controls" width="400" height="300" >
    <source type="video/mp4" src="./_media/video/normal/05.mp4"></source>
</video>

- #### 数据可视化

source: [https://github.com/tnfe/FFCreator/blob/master/examples/chart.js](https://github.com/tnfe/FFCreator/blob/master/examples/chart.js)

<video controls="controls" width="320" height="568" >
  <source type="video/mp4" src="./_media/video/wonder/chart.mp4"></source>
</video>

{"name": "ffcreator", "version": "8.0.1", "description": "FFCreator is a lightweight and flexible short video production library", "main": "lib/index.js", "types": "types/index.d.ts", "scripts": {"test": "jest", "lint": "eslint ./lib --ext .js", "travis": "npm run lint", "coveralls": "jest --coverage && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./coverage", "examples": "node ./examples/", "doc": "docsify serve ./docs", "test-typings": "tsc -b types/tsconfig.json && tsc -b types/test/tsconfig.json"}, "repository": "https://github.com/tnfe/FFCreator", "homepage": "https://tnfe.github.io/FFCreator", "keywords": ["video", "nodejs", "video_production"], "license": "MIT", "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@tweenjs/tween.js": "18.5.0", "@xsstomy/subsrt": "^1.0.0", "browser-or-node": "^1.3.0", "colors": "^1.4.0", "echarts": "5.1.2", "eventemitter3": "^4.0.7", "ffcreator": "^7.5.8", "ffmpeg-probe": "^1.0.6", "ffprobe-installer": "^2.1.5", "fluent-ffmpeg": "^2.1.2", "fs-extra": "^9.0.1", "get-pixels": "^3.3.2", "gl-buffer": "^2.1.2", "gl-texture2d": "^2.1.0", "gl-transition": "^1.13.0", "gl-transitions": "^1.43.0", "inkpaint": "^3.0.3", "left-pad": "^1.3.0", "lodash": "^4.17.20", "lottie-nodejs": "^1.1.5", "mtempy": "^1.0.0", "music-metadata": "^11.7.0", "ndarray": "^1.0.19", "rmfr": "^2.0.0", "siz": "^1.2.2"}, "devDependencies": {"babel-eslint": "^10.1.0", "coveralls": "^3.1.0", "eslint": "^6.8.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^10.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "inquirer": "^7.3.3", "jest": "^26.5.2", "keypress": "^0.2.1", "typescript": "^4.4.2"}, "files": ["dist", "lib", "types"]}